import { Metadata } from "next";
import { Header } from "@/components/Header";
import { <PERSON> } from "@/components/Hero";
import { SearchBar } from "@/components/SearchBar";
import { CategoryGrid } from "@/components/CategoryGrid";
import { FeaturedVendors } from "@/components/FeaturedVendors";
import { HowItWorks } from "@/components/HowItWorks";
import { TestimonialCarousel } from "@/components/TestimonialCarousel";
import { Footer } from "@/components/Footer";

export const metadata: Metadata = {
  title: "Evently - Plan Your Perfect Event | Trusted Marketplace",
  description:
    "Connect with verified vendors, manage your budget, and create unforgettable events with Evently's secure marketplace platform. Trusted by thousands of event planners.",
  keywords:
    "event planning, vendors, marketplace, wedding planning, corporate events, party planning",
  openGraph: {
    title: "Evently - Plan Your Perfect Event",
    description:
      "The trusted marketplace for event planning. Connect with verified vendors and create unforgettable experiences.",
    type: "website",
    url: "https://evently.com",
  },
};

// Mock data - in production, these would come from your API
async function getStats() {
  // Simulate API call
  return {
    vendors: 2000,
    events: 10000,
    payouts: "Secure",
  };
}

async function getCategories() {
  return [
    {
      id: "1",
      name: "Weddings",
      description:
        "Make your special day unforgettable with our wedding specialists",
      icon: "💒",
      vendorCount: 450,
    },
    {
      id: "2",
      name: "Corporate Events",
      description: "Professional events that make lasting impressions",
      icon: "🏢",
      vendorCount: 320,
    },
    {
      id: "3",
      name: "Birthday Parties",
      description: "Celebrate another year with joy and memorable moments",
      icon: "🎂",
      vendorCount: 280,
    },
    {
      id: "4",
      name: "Baby Showers",
      description: "Welcome the little one with a beautiful celebration",
      icon: "👶",
      vendorCount: 150,
    },
    {
      id: "5",
      name: "Graduations",
      description: "Honor achievements with a celebration to remember",
      icon: "🎓",
      vendorCount: 120,
    },
    {
      id: "6",
      name: "Anniversaries",
      description: "Celebrate love and milestones in style",
      icon: "💕",
      vendorCount: 200,
    },
  ];
}

async function getFeaturedVendors() {
  return [
    {
      id: "1",
      name: "Elegant Catering Co.",
      image:
        "https://images.unsplash.com/photo-1555244162-803834f70033?w=400&h=300&fit=crop",
      rating: 4.9,
      reviewCount: 127,
      startingPrice: 2500,
      topTag: "Catering",
      location: "San Francisco, CA",
    },
    {
      id: "2",
      name: "Bloom & Blossom Florists",
      image:
        "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop",
      rating: 4.8,
      reviewCount: 89,
      startingPrice: 800,
      topTag: "Florals",
      location: "Oakland, CA",
    },
    {
      id: "3",
      name: "Capture Moments Photography",
      image:
        "https://images.unsplash.com/photo-1606216794074-735e91aa2c92?w=400&h=300&fit=crop",
      rating: 5.0,
      reviewCount: 203,
      startingPrice: 1200,
      topTag: "Photography",
      location: "San Jose, CA",
    },
    {
      id: "4",
      name: "Grand Ballroom Venues",
      image:
        "https://images.unsplash.com/photo-1519167758481-83f550bb49b3?w=400&h=300&fit=crop",
      rating: 4.7,
      reviewCount: 156,
      startingPrice: 5000,
      topTag: "Venues",
      location: "San Francisco, CA",
    },
  ];
}

export default async function HomePage() {
  // Fetch data server-side for SEO
  const [stats, categories, featuredVendors] = await Promise.all([
    getStats(),
    getCategories(),
    getFeaturedVendors(),
  ]);

  return (
    <div className="min-h-screen">
      <Header />
      <main>
        <Hero stats={stats} />
        <SearchBar categories={categories} />
        <CategoryGrid categories={categories} />
        <FeaturedVendors vendors={featuredVendors} />
        <HowItWorks />
        <TestimonialCarousel />
      </main>
      <Footer />
    </div>
  );
}
