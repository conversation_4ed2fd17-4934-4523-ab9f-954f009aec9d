"use client"

import { motion } from "framer-motion"
import { VendorCard } from "./VendorCard"

interface Vendor {
  id: string
  name: string
  image: string
  rating: number
  reviewCount: number
  startingPrice: number
  topTag: string
  location: string
}

interface FeaturedVendorsProps {
  vendors: Vendor[]
}

export function FeaturedVendors({ vendors }: FeaturedVendorsProps) {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="font-serif text-3xl md:text-4xl font-bold text-secondary mb-4">
            Featured Vendors
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover top-rated vendors trusted by thousands of event planners
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {vendors.map((vendor, index) => (
            <VendorCard key={vendor.id} vendor={vendor} index={index} />
          ))}
        </div>
      </div>
    </section>
  )
}