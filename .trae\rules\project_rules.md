These are the project-specific guardrails the AI agent must follow throughout the Evently codebase so the team can scale safely and predictably.

Architecture & stack conventions

Monorepo layout (recommended)

apps/web (Next.js frontend), packages/common (types/schemas), packages/ui (shared components), scripts/ (seeders/migrations).

TypeScript mandatory everywhere (no any except in well-documented exceptions; PRs with new any require approval).

UI tech stack: React (Next.js), Tailwind CSS for styling, Framer Motion for micro-interactions, React Query (or SWR) for server state caching.

State & data fetching

Use server components or SSR for SEO-critical pages (landing), use client components + React Query for live dashboards and user flows.

Use query keys and cache invalidation patterns; mutate and then invalidateQueries for consistency.

Firestore data & naming conventions

Collections: users, vendors, products, eventBuckets, orders, vendorVerifications, siteContent, adminActions, activityLog

Document IDs: use push-style ids for user-generated content, but deterministic ids (e.g., order_{bucketId}_{timestamp}) allowed when helpful for search/tracing.

Timestamps & versioning

Every document must have createdAt, updatedAt (Firestore server timestamps) and version (integer incremented on each write).

Monolithic documents limited to 1MB

Avoid huge arrays in single docs — use subcollections for chat/messages/activity if they can grow.

Security & access control

Custom claims

Assign role via a server admin endpoint. Never allow client to set role.

Sensitive ops

Reserve these to server routes invoked by authenticated users: createOrder, modifyEscrow, approveVendor, changeUserRole, payoutVendor.

Firestore security rules

Implement rules that mirror but do not replace server checks. E.g., allow update: if request.auth.uid == resource.data.ownerId && request.resource.data.status in ['draft','submitted'].

Storage path conventions

Store user assets under gs://bucket/users/{uid}/{type}/{filename} and vendor docs under gs://bucket/vendors/{vendorId}/kyc/{filename}. Use signed URLs for controlled access.

Financials, reservations, inventory

Server-only financial math

Commission, serviceFee, currency conversions, taxes: compute on the server and persist breakdowns in orders (fees: {commission,serviceFee,tax}).

Reservation flow

Reservation request lifecycle: reservation_requested -> vendor_responded -> reserved | reservation_rejected.

Model reservation object inside orders: {requestedDates, vendorDecisionBy, serviceFee, vendorId, decision, log[]}.

Inventory & availability

Enforce availability checks in transactions against products.{availableQuantity}. For high-value venue rentals, require an escrow flag and vendor confirmation.

API design & endpoints

RESTful naming & behaviour

Use /api/buckets, /api/orders, /api/vendors, /api/vendors/suggest, /api/admin/stats.

Auth

Every API checks Firebase ID token; admin endpoints further validate custom claims.

Pagination & filtering

Use cursor-based pagination (limit + startAfter) for long lists. Always return nextCursor.

Rate limiting

Implement simple per-user rate limiting server-side for endpoints that could be abused (search, vendor suggestions).

Developer experience & workflows

Use Firebase emulators in development

Provide npm run dev:emulator script that seeds mock data and runs web against local emulators.

Seeding & migrations

Maintain scripts/seed.ts and scripts/migrate.ts with idempotent seeds and traceable migration history.

CI/CD

PRs require lint, typecheck, tests, and emulator-based integration tests. Merge only after 1 approving review.

Branching

Trunk-based with short-lived feature branches; semantic PR titles; chore/release, feat/*, fix/* conventional commits.

Precommit & hooks

Husky + lint-staged running eslint --fix, prettier, and unit tests for changed files.

Testing & quality

Tests

Unit tests for pure logic, integration tests for API endpoints using Firebase emulator, E2E for critical flows (login, create bucket, vendor accept) using Playwright or Cypress.

Coverage

Maintain >80% coverage for backend logic that handles money/reservations; >60% for UI.

Static analysis

Use eslint, TypeScript strict mode, and vulnerability scans on CI.

Observability, logging & monitoring

Structured logs

All server endpoints log structured JSON: {level, ts, uid, route, action, id, message, meta}.

Metrics

Capture simple metrics: buckets created/day, reservation acceptance rate, pending verifications, failed payments (mock), error rates.

Alerts

Set alerts for error-rate spike or emulator parity failures in staging.

UX, accessibility, and localization

Timezone handling

Store all datetimes in ISO8601 + timezone info. Normalize to UTC for storage; always display localized to user timezone with explicit label.

Accessibility

All interactive elements keyboard-navigable, correct ARIA roles, color contrast checks in PRs.

Localization-ready

Use i18n file structure from day one (even if English-only initially). Externalize copy.

Cost & performance

Firestore cost-awareness

Model reads/writes to minimize document fan-out. Avoid N+1 reads by denormalizing computed totals and using batched reads where possible.

Indexing

Define required composite indexes early and test queries in emulator. Keep index usage reasonable.

Image handling

Use next/image with Firebase Storage signed URLs or a short CDN origin; generate thumbnails server-side if necessary.

Deployment & environment management

Envs

NEXT_PUBLIC_* only for non-secret values. Secret keys kept in CI/CD secrets manager and server envs (do not commit .env).

Staging & prod parity

Staging must mirror prod rules and config (security rules, Cloud Functions) and run periodic seeding/cleanup jobs.

Documentation & handover

README

Top-level README must include dev setup, emulator commands, seeding steps, and CI requirements.

Architecture doc

Maintain a short architecture.md describing data model, critical endpoints, and reservation/escrow flow.

Onboarding

Include a developer-checklist.md for new contributors: run emulators, seed data, run tests.

Enforcement & "how the agent knows it's following rules"

Every PR that touches server-authoritative flows must include:

A short checklist in PR description referencing which points from the User Rule and Project Rule it satisfies (e.g., “uses transaction, adds audit log, validates input with Zod”).

Tests demonstrating the happy path + at least 2 edge cases (e.g., duplicate reservation attempts; unauthorized role action).

Screenshots or a short video of the flow running against the emulator (for UIs that perform server-authoritative operations).

CI must enforce lint, typecheck, emulator tests; merging is blocked until green.

Short “Do this / Don’t do this” quick reference

Do this

Validate inputs server-side with Zod.

Run Firestore writes in transactions for reservations and payments.

Use custom claims for roles and check them server-side.

Seed and test against Firebase emulator on every PR.

Produce an audit log on every sensitive action.

Don’t do this

Perform inventory or escrow writes directly from client without server verification.

Put service keys or Admin SDK usage into client bundles.

Assume Firestore security rules alone are sufficient for business logic.

Store long-growing lists in a single document without subcollections.

Skip integration tests for reservation/payment flows.