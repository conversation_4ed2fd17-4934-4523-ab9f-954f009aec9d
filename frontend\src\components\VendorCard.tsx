"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { HiStar } from "react-icons/hi"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

interface Vendor {
  id: string
  name: string
  image: string
  rating: number
  reviewCount: number
  startingPrice: number
  topTag: string
  location: string
}

interface VendorCardProps {
  vendor: Vendor
  index?: number
}

export function VendorCard({ vendor, index = 0 }: VendorCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ scale: 1.02 }}
    >
      <Card className="overflow-hidden hover:shadow-lg transition-shadow">
        <div className="relative h-48">
          <Image
            src={vendor.image}
            alt={vendor.name}
            fill
            className="object-cover"
          />
          <div className="absolute top-3 left-3">
            <Badge variant="secondary">{vendor.topTag}</Badge>
          </div>
        </div>
        
        <CardContent className="p-4">
          <h3 className="font-semibold text-lg text-secondary mb-2 line-clamp-1">
            {vendor.name}
          </h3>
          
          <div className="flex items-center mb-2">
            <div className="flex items-center">
              <HiStar className="h-4 w-4 text-yellow-400 fill-current" />
              <span className="ml-1 text-sm font-medium">{vendor.rating}</span>
              <span className="ml-1 text-sm text-gray-500">
                ({vendor.reviewCount} reviews)
              </span>
            </div>
          </div>
          
          <p className="text-sm text-gray-600 mb-3">{vendor.location}</p>
          
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm text-gray-500">Starting from</span>
              <div className="font-semibold text-primary">
                ${vendor.startingPrice.toLocaleString()}
              </div>
            </div>
            
            <Button size="sm" asChild>
              <Link href={`/vendors/${vendor.id}`}>View Details</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}