import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: "Evently - Plan Your Perfect Event",
    template: "%s | Evently",
  },
  description:
    "The trusted marketplace for event planning. Connect with verified vendors, manage your budget, and create unforgettable experiences.",
  keywords:
    "event planning, vendors, marketplace, wedding planning, corporate events, party planning, escrow, secure payments",
  authors: [{ name: "Evently Team" }],
  creator: "Evently",
  publisher: "Evently",
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://evently.com",
    siteName: "Evently",
    title: "Evently - Plan Your Perfect Event",
    description:
      "The trusted marketplace for event planning. Connect with verified vendors and create unforgettable experiences.",
  },
  twitter: {
    card: "summary_large_image",
    title: "Evently - Plan Your Perfect Event",
    description:
      "The trusted marketplace for event planning. Connect with verified vendors and create unforgettable experiences.",
    creator: "@evently",
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="theme-color" content="#296A25" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
