"use client"

import { useState, useCallback } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON><PERSON><PERSON>, HiLocationMarker, HiCalendar } from "react-icons/hi"
import * as DropdownMenu from "@radix-ui/react-dropdown-menu"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { debounce } from "@/lib/utils"

interface SearchSuggestion {
  id: string
  name: string
  type: "vendor" | "category" | "location"
}

interface SearchBarProps {
  categories: Array<{ id: string; name: string }>
}

export function SearchBar({ categories }: SearchBarProps) {
  const [eventType, setEventType] = useState("")
  const [location, setLocation] = useState("")
  const [date, setDate] = useState("")
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const fetchSuggestions = useCallback(
    debounce(async (query: string, type: string) => {
      if (!query.trim()) {
        setSuggestions([])
        setShowSuggestions(false)
        return
      }

      setIsLoading(true)
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/search/suggestions?q=${encodeURIComponent(query)}&type=${type}`
        )
        const data = await response.json()
        setSuggestions(data.suggestions || [])
        setShowSuggestions(true)
      } catch (error) {
        console.error("Failed to fetch suggestions:", error)
        setSuggestions([])
      } finally {
        setIsLoading(false)
      }
    }, 300),
    []
  )

  const handleSearch = () => {
    const params = new URLSearchParams()
    if (eventType) params.set("type", eventType)
    if (location) params.set("location", location)
    if (date) params.set("date", date)
    
    window.location.href = `/search?${params.toString()}`
  }

  return (
    <motion.section
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.2 }}
      className="py-16 bg-white"
    >
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="font-serif text-3xl font-bold text-center text-secondary mb-8">
            Find the perfect vendors for your event
          </h2>
          
          <div className="bg-white rounded-lg shadow-lg border p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Event Type Dropdown */}
              <div className="relative">
                <DropdownMenu.Root>
                  <DropdownMenu.Trigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-between h-12 text-left"
                    >
                      <div className="flex items-center">
                        <HiSearch className="h-4 w-4 mr-2 text-gray-400" />
                        <span className={eventType ? "text-secondary" : "text-gray-500"}>
                          {eventType || "Event Type"}
                        </span>
                      </div>
                    </Button>
                  </DropdownMenu.Trigger>
                  <DropdownMenu.Portal>
                    <DropdownMenu.Content className="w-56 bg-white rounded-md shadow-lg border p-1 z-50">
                      {categories.map((category) => (
                        <DropdownMenu.Item
                          key={category.id}
                          className="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 rounded"
                          onSelect={() => setEventType(category.name)}
                        >
                          {category.name}
                        </DropdownMenu.Item>
                      ))}
                    </DropdownMenu.Content>
                  </DropdownMenu.Portal>
                </DropdownMenu.Root>
              </div>

              {/* Location Input */}
              <div className="relative">
                <div className="relative">
                  <HiLocationMarker className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Location"
                    value={location}
                    onChange={(e) => {
                      setLocation(e.target.value)
                      fetchSuggestions(e.target.value, "location")
                    }}
                    className="pl-10 h-12"
                  />
                </div>
                
                {/* Suggestions Dropdown */}
                {showSuggestions && suggestions.length > 0 && (
                  <div className="absolute top-full left-0 right-0 bg-white border rounded-md shadow-lg mt-1 z-50">
                    {suggestions.slice(0, 6).map((suggestion) => (
                      <button
                        key={suggestion.id}
                        className="w-full px-3 py-2 text-left hover:bg-gray-100 text-sm"
                        onClick={() => {
                          setLocation(suggestion.name)
                          setShowSuggestions(false)
                        }}
                      >
                        {suggestion.name}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Date Input */}
              <div className="relative">
                <HiCalendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="date"
                  value={date}
                  onChange={(e) => setDate(e.target.value)}
                  className="pl-10 h-12"
                />
              </div>

              {/* Search Button */}
              <Button onClick={handleSearch} className="h-12">
                Search
              </Button>
            </div>
          </div>
        </div>
      </div>
    </motion.section>
  )
}