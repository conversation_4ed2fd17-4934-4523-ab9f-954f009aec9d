import Link from "next/link"
import { HiMail, HiPhone, HiLocationMarker } from "react-icons/hi"

export function Footer() {
  const footerLinks = {
    Company: [
      { href: "/about", label: "About Us" },
      { href: "/careers", label: "Careers" },
      { href: "/press", label: "Press" },
      { href: "/blog", label: "Blog" },
    ],
    Support: [
      { href: "/help", label: "Help Center" },
      { href: "/contact", label: "Contact Us" },
      { href: "/safety", label: "Safety" },
      { href: "/community", label: "Community" },
    ],
    Legal: [
      { href: "/terms", label: "Terms of Service" },
      { href: "/privacy", label: "Privacy Policy" },
      { href: "/cookies", label: "Cookie Policy" },
      { href: "/guidelines", label: "Guidelines" },
    ],
  }

  return (
    <footer className="bg-secondary text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
                <span className="text-white font-bold text-lg">E</span>
              </div>
              <span className="font-serif text-xl font-bold">Evently</span>
            </div>
            <p className="text-gray-300 mb-6 max-w-md">
              The trusted marketplace for event planning. Connect with verified vendors, 
              manage your budget, and create unforgettable experiences.
            </p>
            <div className="space-y-2">
              <div className="flex items-center text-gray-300">
                <HiMail className="h-4 w-4 mr-2" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center text-gray-300">
                <HiPhone className="h-4 w-4 mr-2" />
                <span>1-800-EVENTLY</span>
              </div>
              <div className="flex items-center text-gray-300">
                <HiLocationMarker className="h-4 w-4 mr-2" />
                <span>San Francisco, CA</span>
              </div>
            </div>
          </div>

          {/* Links Sections */}
          {Object.entries(footerLinks).map(([title, links]) => (
            <div key={title}>
              <h3 className="font-semibold mb-4">{title}</h3>
              <ul className="space-y-2">
                {links.map((link) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <hr className="border-gray-700 my-8" />

        <div className="flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-300 text-sm">
            © 2024 Evently. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link href="/terms" className="text-gray-300 hover:text-white text-sm">
              Terms
            </Link>
            <Link href="/privacy" className="text-gray-300 hover:text-white text-sm">
              Privacy
            </Link>
            <Link href="/cookies" className="text-gray-300 hover:text-white text-sm">
              Cookies
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}