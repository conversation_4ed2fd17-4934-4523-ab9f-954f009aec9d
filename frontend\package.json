{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.1.3", "framer-motion": "^12.23.12", "next": "15.5.0", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "eslint": "^9", "eslint-config-next": "15.5.0", "postcss": "^8.5.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.0", "typescript": "^5"}}