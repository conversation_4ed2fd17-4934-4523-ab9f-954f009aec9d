"use client"

import { motion } from "framer-motion"
import { HiPlus, HiShoppingCart, HiShieldCheck } from "react-icons/hi"
import { Card, CardContent } from "@/components/ui/card"

export function HowItWorks() {
  const steps = [
    {
      icon: <HiPlus className="h-8 w-8" />,
      title: "Create Event Bucket",
      description: "Start by creating your event bucket and setting your budget. Define your event details and requirements."
    },
    {
      icon: <HiShoppingCart className="h-8 w-8" />,
      title: "Add Vendors & Items",
      description: "Browse and add trusted vendors to your bucket. Compare prices, read reviews, and make informed decisions."
    },
    {
      icon: <HiShieldCheck className="h-8 w-8" />,
      title: "Checkout with Escrow",
      description: "Complete your booking with secure escrow protection. Your payment is held safely until services are delivered."
    }
  ]

  return (
    <section className="py-16 bg-light">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="font-serif text-3xl md:text-4xl font-bold text-secondary mb-4">
            How It Works
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Plan your perfect event in three simple steps with complete peace of mind
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {steps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.2 }}
            >
              <Card className="text-center h-full border-0 shadow-lg">
                <CardContent className="p-8">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-primary text-white rounded-full mb-6">
                    {step.icon}
                  </div>
                  <h3 className="font-serif text-xl font-semibold text-secondary mb-4">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}