"use client"

import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"

interface HeroProps {
  stats: {
    vendors: number
    events: number
    payouts: string
  }
}

export function Hero({ stats }: HeroProps) {
  const statCards = [
    { label: "Vendors", value: `${stats.vendors.toLocaleString()}+` },
    { label: "Events Created", value: `${stats.events.toLocaleString()}+` },
    { label: "Trusted Payouts", value: stats.payouts },
  ]

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-light to-white py-20">
      <div className="container mx-auto px-4">
        <div className="text-center">
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="font-serif text-4xl md:text-6xl font-bold text-secondary mb-6"
          >
            Plan your event —{" "}
            <span className="text-primary">everything in one bucket</span>
          </motion.h1>
          
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-lg md:text-xl text-gray-600 mb-8 max-w-2xl mx-auto"
          >
            Connect with trusted vendors, manage your budget, and create unforgettable events with our secure marketplace platform.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center mb-16"
          >
            <Button size="lg" asChild>
              <Link href="/create-event">Create Event</Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/explore">Explore Vendors</Link>
            </Button>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto"
          >
            {statCards.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                whileHover={{ scale: 1.05 }}
              >
                <Card className="border-0 shadow-lg bg-white/80 backdrop-blur">
                  <CardContent className="p-6 text-center">
                    <div className="text-2xl md:text-3xl font-bold text-primary mb-2">
                      {stat.value}
                    </div>
                    <div className="text-sm text-gray-600">{stat.label}</div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
}