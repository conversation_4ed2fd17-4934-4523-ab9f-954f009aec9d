"use client"

import { useState } from "react"
import Link from "next/link"
import { motion } from "framer-motion"
import { HiMenu, HiX } from "react-icons/hi"
import * as Dialog from "@radix-ui/react-dialog"
import { Button } from "@/components/ui/button"

export function Header() {
  const [isOpen, setIsOpen] = useState(false)

  const navItems = [
    { href: "/", label: "Home" },
    { href: "/explore", label: "Explore" },
    { href: "/pricing", label: "Pricing" },
    { href: "/support", label: "Support" },
  ]

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className="sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60"
    >
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        <Link href="/" className="flex items-center space-x-2">
          <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
            <span className="text-white font-bold text-lg">E</span>
          </div>
          <span className="font-serif text-xl font-bold text-secondary">Evently</span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          {navItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className="text-sm font-medium text-secondary hover:text-primary transition-colors"
            >
              {item.label}
            </Link>
          ))}
        </nav>

        {/* Desktop CTA */}
        <div className="hidden md:flex items-center space-x-4">
          <Link href="/signin" className="text-sm font-medium text-secondary hover:text-primary">
            Sign in
          </Link>
          <Button asChild>
            <Link href="/signup">Get Started</Link>
          </Button>
        </div>

        {/* Mobile Menu */}
        <Dialog.Root open={isOpen} onOpenChange={setIsOpen}>
          <Dialog.Trigger asChild>
            <Button variant="ghost" size="sm" className="md:hidden">
              <HiMenu className="h-6 w-6" />
            </Button>
          </Dialog.Trigger>
          <Dialog.Portal>
            <Dialog.Overlay className="fixed inset-0 z-50 bg-black/50" />
            <Dialog.Content className="fixed right-0 top-0 z-50 h-full w-3/4 max-w-sm bg-white p-6 shadow-lg">
              <div className="flex items-center justify-between mb-8">
                <span className="font-serif text-xl font-bold text-secondary">Evently</span>
                <Dialog.Close asChild>
                  <Button variant="ghost" size="sm">
                    <HiX className="h-6 w-6" />
                  </Button>
                </Dialog.Close>
              </div>
              <nav className="flex flex-col space-y-4">
                {navItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="text-lg font-medium text-secondary hover:text-primary"
                    onClick={() => setIsOpen(false)}
                  >
                    {item.label}
                  </Link>
                ))}
                <hr className="my-4" />
                <Link
                  href="/signin"
                  className="text-lg font-medium text-secondary hover:text-primary"
                  onClick={() => setIsOpen(false)}
                >
                  Sign in
                </Link>
                <Button asChild className="w-full">
                  <Link href="/signup">Get Started</Link>
                </Button>
              </nav>
            </Dialog.Content>
          </Dialog.Portal>
        </Dialog.Root>
      </div>
    </motion.header>
  )
}